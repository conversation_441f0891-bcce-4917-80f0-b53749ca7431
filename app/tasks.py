import logging
import re
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional
from concurrent.futures import ThreadPoolExecutor
from flask import current_app
from openai import OpenAI
from jinja2 import Environment, FileSystemLoader
from app import celery, db
from app.models import AdaptationTask
from app.utils import (
    split_text_into_chapters,
    create_chapter_files,
    BlockError,
    retry_operation,
    update_task_progress,
    merge_adapted_files,
    find_similar_chunks,
    filter_think_tags,
    get_smart_previous_context,
    wait_for_previous_chapter,
    filter_chapter_files,
    get_chapter_files_sorted,
    extract_chapter_number,
    check_processed_chapters
)

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class TextRewriter:
    def __init__(self, api_keys: List[str], character: str, book_name: str, channel: str, person: str, key_func):
        """Initialize the TextRewriter with API keys and model configuration."""
        self.client = None
        self.model = None
        self.api_keys = api_keys
        self.current_key_index = 0
        self.model_name = "gemini-2.5-pro-preview-06-05"
        self.character = character
        self.book_name = book_name
        self.channel = channel
        self.person = person
        self.key_func = key_func

        # 初始化Jinja2模板环境
        template_dir = Path(__file__).parent / 'prompt_templates'
        self.jinja_env = Environment(loader=FileSystemLoader(str(template_dir)))

        self.configure_api()

    def get_system_prompt(self) -> str:
        """获取系统提示词"""
        template = self.jinja_env.get_template('system_prompt.j2')
        return template.render(
            person=self.person,
            channel=self.channel
        )

    def configure_api(self) -> None:
        # 从配置中获取API密钥
        api_key = current_app.config.get('API_KEYS', [''])[0] if current_app.config.get('API_KEYS') else ''
        self.client = OpenAI(
            api_key=api_key,
            base_url='https://oapi.xmly.dev/v1'
        )

    def send_chat_completion(self, messages: List[Dict[str, str]], max_retries: int = 1) -> Optional[str]:
        for retry in range(max_retries):
            try:
                response = self.client.chat.completions.create(
                    model=self.model_name,
                    messages=messages,
                    temperature=1,
                    top_p=0.95,
                    max_tokens=32000,
                    timeout=800,
                )

                if 'No candidates returned' in str(response):
                    raise BlockError

                # 过滤掉<think></think>标签及其内容
                content = response.choices[0].message.content
                filtered_content = filter_think_tags(content)

                return filtered_content

            except BlockError as e:
                raise e
            except Exception as e:
                logger.error(f"API error: {str(e)}")
                if retry < max_retries - 1:
                    self.rotate_api_key()
                else:
                    return None

    def rotate_api_key(self) -> None:
        """Rotate to the next API key."""
        self.configure_api()



    def initial_rewrite(self, text: str, pre: str) -> Dict[str, str]:
        """First rewrite of the input text using the Gemini model."""
        template = self.jinja_env.get_template('initial_prompt.j2')
        initial_prompt = template.render(
            pre_text=pre,
            character=self.character,
            text_length=len(text),
            min_output_length=int(len(text) * 0.7),
            max_output_length=int(len(text) * 0.8),
            text=text
        )

        logger.info(f"-------------------Initial rewriting-------------------")
        try:
            logger.info(f"Initial rewriting context length: {len(text)}")
            messages = [
                {"role": "system", "content": self.get_system_prompt()},
                {"role": "user", "content": initial_prompt}
            ]
            response = self.send_chat_completion(messages)
            if response:
                logger.info(f"Initial rewriting result length: {len(response)}")
                logger.debug(f"Initial rewriting result preview: {response[:200]}...")
            else:
                logger.warning("Initial rewriting returned empty response")
            return {
                "rewritten_text": response,
                "original_prompt": initial_prompt
            } if response else {"rewritten_text": None, "original_prompt": initial_prompt}
        except BlockError as e:
            raise e
        except Exception as e:
            logger.error(f"Error during initial rewriting: {str(e)}")
            self.rotate_api_key()
            return {"rewritten_text": None, "original_prompt": initial_prompt}

    def optimize_rewrite(self, original_text: str, first_rewrite: str) -> Dict[str, str]:
        """Optimize the first rewrite using the Gemini model."""
        template = self.jinja_env.get_template('optimize_prompt.j2')
        review_prompt = template.render(
            original_text=original_text,
            first_rewrite=first_rewrite
        )

        logger.info(f"-------------------Optimization rewriting-------------------")
        try:
            messages = [
                {"role": "system", "content": self.get_system_prompt()},
                {"role": "user", "content": review_prompt}
            ]
            response = self.send_chat_completion(messages)
            logger.info(f"Optimization rewriting result length: {len(response) if response else 0}")
            return {
                "rewritten_text": response,
                "original_prompt": review_prompt
            }
        except BlockError as e:
            raise e
        except Exception as e:
            logger.error(f"Error during optimization rewriting: {str(e)}")
            self.rotate_api_key()
            return {"rewritten_text": None, "original_prompt": review_prompt}

    def further_rewrite(self, original_text: str, origin_prompt: str, rewritten_text: str) -> Dict[str, str]:
        """Perform additional rewriting based on similar text chunks."""
        similar_chunks = find_similar_chunks(rewritten_text, original_text)
        logger.info(f"-------------------Further rewriting-------------------")
        if not similar_chunks:
            logger.info("No text chunks requiring further rewriting found.")
            return {"rewritten_text": rewritten_text}

        # 准备相似文本块数据
        chunk_data = []
        for rewrite, _, sim in similar_chunks:
            clean_text = rewrite.replace('\n', ' ')
            chunk_data.append({
                'text': clean_text,
                'similarity': sim
            })

        template = self.jinja_env.get_template('further_rewrite_prompt.j2')
        prompt = template.render(similar_chunks=chunk_data)

        try:
            messages = [
                {"role": "system", "content": self.get_system_prompt()},
                {"role": "user", "content": origin_prompt},
                {"role": "assistant", "content": rewritten_text},
                {"role": "user", "content": prompt}
            ]
            response = self.send_chat_completion(messages)
            logger.info(f"Further rewriting result length: {len(response) if response else 0}")
            return {"rewritten_text": response}

        except BlockError as e:
            raise e
        except Exception as e:
            logger.error(f"Error during further rewriting: {str(e)}")
            self.rotate_api_key()
            return {"rewritten_text": None}

    def _process_chapter_text(self, chapter_text: str, pre_text: str, num_attempts: int, chapter_name: str) -> Optional[str]:
        """处理章节文本并返回最佳结果"""
        results = []
        max_attempts = num_attempts * 2
        attempts = 0

        while len(results) < num_attempts and attempts < max_attempts:
            attempts += 1
            logger.info(f"Attempt {attempts} for {chapter_name}")

            # 尝试重写
            result = self._attempt_rewrite_chapter(chapter_text, pre_text)
            if result:
                results.append(result)
                logger.info(f"Successful attempt {attempts} for {chapter_name} finished with {len(result)} characters.")

        if not results:
            logger.error(f"Failed to rewrite {chapter_name} after {max_attempts} attempts")
            # 降级策略：返回原文本而不是None
            logger.warning(f"使用降级策略：返回原始文本作为{chapter_name}的结果")
            return chapter_text

        # 返回最长的结果
        best_result = max(results, key=lambda x: len(x))
        logger.info(f"成功处理{chapter_name}，选择了长度为{len(best_result)}的最佳结果")
        return best_result

    def _attempt_rewrite_chapter(self, chapter_text: str, pre_text: str,
                               enable_optimization: bool = True, max_retries: int = 10, delay: float = 1.0) -> Optional[str]:
        """尝试一次章节文本的重写"""
        # 尝试initial_rewrite
        initial_result = retry_operation(
            self.initial_rewrite,
            max_retries,
            delay,
            chapter_text,
            pre_text
        )
        if not initial_result:
            logger.error(f"Initial rewriting failed after {max_retries} retries for chapter text")
            return None

        if enable_optimization:
            # 尝试optimize_rewrite
            optimized_result = retry_operation(
                self.optimize_rewrite,
                max_retries,
                delay,
                chapter_text,
                initial_result["rewritten_text"]
            )
            if not optimized_result:
                logger.error(f"Optimization failed after {max_retries} retries for chapter text")
                optimized_result = {"rewritten_text": initial_result["rewritten_text"]}
        else:
            optimized_result = {"rewritten_text": initial_result["rewritten_text"]}

        # 尝试further_rewrite
        further_result = retry_operation(
            self.further_rewrite,
            max_retries,
            delay,
            chapter_text,
            initial_result['original_prompt'],
            optimized_result["rewritten_text"]
        )
        if not further_result:
            logger.error(f"Further rewriting failed after {max_retries} retries for chapter text")
            further_result = {"rewritten_text": initial_result["rewritten_text"]}

        return further_result["rewritten_text"]


class ProgressAwareTextRewriter(TextRewriter):
    """支持进度回调的TextRewriter"""

    def __init__(self, *args, **kwargs):
        self.progress_callback = kwargs.pop('progress_callback', None)
        super().__init__(*args, **kwargs)





    def process_chapters_concurrently(self, input_dir, output_dir=None, num_attempts=2,
                                    start_chapter=None, end_chapter=None,
                                    max_workers=3, progress_callback=None):
        """并发处理章节（每章作为独立单位）"""
        if progress_callback:
            self.progress_callback = progress_callback

        input_path = Path(input_dir)
        output_path = Path(output_dir) if output_dir else input_path
        output_path.mkdir(parents=True, exist_ok=True)

        # 获取所有.txt文件但排除包含rewrite的文件
        input_files = get_chapter_files_sorted(input_path, exclude_rewrite=True)

        # 应用章节过滤
        input_files = filter_chapter_files(input_files, start_chapter, end_chapter)

        if not input_files:
            if self.progress_callback:
                self.progress_callback(0, 0, f"没有找到要处理的文件")
            return

        total_chapters = len(input_files)

        # 统计已处理的章节
        already_processed, pending_files = check_processed_chapters(input_files, output_path)

        # 初始进度包含已处理的章节
        processed_chapters = already_processed

        if self.progress_callback and already_processed > 0:
            self.progress_callback(
                processed_chapters,
                total_chapters,
                f"发现{already_processed}个已处理章节，继续处理剩余{len(pending_files)}章"
            )

        if not pending_files:
            if self.progress_callback:
                self.progress_callback(total_chapters, total_chapters, "所有章节已处理完成")
            return

        # 使用改进的并发处理策略
        self._process_chapters_with_smart_concurrency(
            pending_files, output_path, num_attempts,
            processed_chapters, total_chapters, max_workers
        )

    def _process_chapters_with_smart_concurrency(self, pending_files, output_path, num_attempts,
                                               initial_processed, total_chapters, max_workers):
        """智能并发处理章节，考虑章节间的依赖关系"""
        import threading

        processed_chapters = initial_processed
        completed_chapters = set()  # 记录已完成的章节号
        processing_lock = threading.Lock()  # 用于线程安全的计数更新

        # 按章节号分组，确保顺序处理的依赖关系
        chapter_files = {}
        for file_path in pending_files:
            chapter_num = extract_chapter_number(file_path)
            chapter_files[chapter_num] = file_path

        # 获取所有需要处理的章节号，按顺序排列
        chapter_numbers = sorted(chapter_files.keys())

        def process_chapter_with_dependency(chapter_num):
            """处理单个章节，考虑前置依赖"""
            file_path = chapter_files[chapter_num]

            try:
                # 等待前一章完成（如果需要）
                if chapter_num > 1:
                    wait_for_previous_chapter(chapter_num - 1, output_path, completed_chapters, timeout=300)

                # 处理当前章节
                result = self._process_single_chapter_with_smart_context(
                    file_path, output_path, num_attempts, chapter_num
                )

                # 线程安全地更新进度
                with processing_lock:
                    nonlocal processed_chapters
                    processed_chapters += 1
                    completed_chapters.add(chapter_num)

                    if self.progress_callback:
                        self.progress_callback(
                            processed_chapters,
                            total_chapters,
                            f"完成第{chapter_num}章处理"
                        )

                logger.info(f"Successfully processed chapter {chapter_num}")
                return result

            except Exception as e:
                logger.error(f"处理第{chapter_num}章失败: {str(e)}")
                with processing_lock:
                    if self.progress_callback:
                        self.progress_callback(
                            processed_chapters,
                            total_chapters,
                            f"第{chapter_num}章处理失败: {str(e)}"
                        )
                raise

        # 使用线程池处理，但限制并发数以避免过多等待
        with ThreadPoolExecutor(max_workers=min(max_workers, 2)) as executor:
            # 提交所有任务
            futures = []
            for chapter_num in chapter_numbers:
                future = executor.submit(process_chapter_with_dependency, chapter_num)
                futures.append((chapter_num, future))

            # 等待所有任务完成
            for chapter_num, future in futures:
                try:
                    future.result()
                except Exception as e:
                    logger.error(f"Chapter {chapter_num} processing failed: {str(e)}")



    def _process_single_chapter_with_smart_context(self, file_path, output_path, num_attempts, chapter_num):
        """智能上下文的单章节处理"""
        # 获取智能前文
        pre_text = get_smart_previous_context(output_path, chapter_num)

        # 读取章节内容
        try:
            chapter_text = file_path.read_text(encoding='utf-8')
            logger.info(f"Processing chapter {chapter_num} with smart context (length: {len(chapter_text)} characters)")
        except Exception as e:
            logger.error(f"Error reading chapter {chapter_num}: {str(e)}")
            raise

        # 处理章节内容
        best_result = self._process_combined_text(
            chapter_text,
            pre_text,
            num_attempts,
            f"chapter_{chapter_num}"
        )

        # 保存结果
        output_name = f"chapter_{chapter_num:03d}_rewrite.txt"
        output_file_path = output_path / output_name
        output_file_path.write_text(best_result, encoding='utf-8')

        logger.info(f"Successfully saved chapter {chapter_num}: {output_name} (length: {len(best_result)} characters)")
        return best_result


@celery.task(bind=True)
def process_adaptation_task(self, task_id):
    """处理故事优化任务"""
    try:
        # 获取任务信息
        task = AdaptationTask.query.get(task_id)
        if not task:
            raise Exception(f"Task {task_id} not found")

        # 更新任务状态
        task.status = 'processing'
        task.started_at = datetime.utcnow()
        task.celery_task_id = self.request.id
        db.session.commit()

        # 更新进度
        self.update_state(state='PROGRESS', meta={'current': 0, 'total': 100, 'status': '开始处理...'})

        # 读取原始文件
        with open(task.file_path, 'r', encoding='utf-8') as f:
            text_content = f.read()

        # 分割章节
        self.update_state(state='PROGRESS', meta={'current': 10, 'total': 100, 'status': '分析章节结构...'})
        chapters = split_text_into_chapters(text_content)

        if not chapters:
            raise Exception("无法识别章节结构")

        # 更新总章节数
        task.total_chapters = len(chapters)
        db.session.commit()

        # 创建章节文件
        self.update_state(state='PROGRESS', meta={'current': 20, 'total': 100, 'status': '创建章节文件...'})
        chapters_dir, _ = create_chapter_files(chapters, task.file_path)

        # 初始化TextRewriter
        self.update_state(state='PROGRESS', meta={'current': 30, 'total': 100, 'status': '初始化故事优化引擎...'})

        api_keys = current_app.config['API_KEYS']
        rewriter = ProgressAwareTextRewriter(
            api_keys=api_keys,
            character=task.character,
            book_name=task.book_name,
            channel=task.channel,
            person=task.person,
            key_func=lambda s: int(re.search(r'\d+', s.stem).group()) if re.search(r'\d+', s.stem) else None
        )

        # 设置输出目录
        output_dir = Path(task.file_path).parent / f"{Path(task.file_path).stem}_adapted"
        output_dir.mkdir(parents=True, exist_ok=True)

        # 开始故事优化处理
        self.update_state(state='PROGRESS', meta={'current': 40, 'total': 100, 'status': '开始故事优化处理...'})

        # 使用并发处理方法进行故事优化
        try:
            # 并发处理章节，每章作为独立单位
            rewriter.process_chapters_concurrently(
                input_dir=chapters_dir,
                output_dir=str(output_dir),
                num_attempts=1,
                start_chapter=getattr(task, 'start_chapter', None),
                end_chapter=getattr(task, 'end_chapter', None),
                max_workers=3,  # 并发处理3个章节
                progress_callback=lambda current, total, status: update_task_progress(
                    self, task, current, total, status, 40, 90
                )
            )
        except Exception as e:
            logger.error(f"故事优化处理失败: {str(e)}")
            raise

        # 合并输出文件
        self.update_state(state='PROGRESS', meta={'current': 95, 'total': 100, 'status': '合并输出文件...'})
        final_output_path = merge_adapted_files(output_dir, task)

        # 更新任务完成状态
        task.status = 'completed'
        task.completed_at = datetime.utcnow()
        task.output_path = final_output_path
        task.progress = 100
        db.session.commit()

        self.update_state(state='SUCCESS', meta={'current': 100, 'total': 100, 'status': '故事优化完成！'})

        return {
            'status': 'completed',
            'output_path': final_output_path,
            'total_chapters': task.total_chapters,
            'processed_chapters': task.processed_chapters
        }

    except Exception as e:
        error_msg = str(e)
        logger.error(f"任务处理失败: {error_msg}")

        # 更新任务失败状态
        if 'task' in locals():
            task.status = 'failed'
            task.error_message = error_msg
            task.completed_at = datetime.utcnow()
            db.session.commit()

        # 使用字符串而不是异常对象来避免序列化问题
        self.update_state(state='FAILURE', meta={'error': error_msg, 'status': '任务失败'})
        # 抛出一个简单的异常，避免复杂对象序列化问题
        raise Exception(error_msg)
