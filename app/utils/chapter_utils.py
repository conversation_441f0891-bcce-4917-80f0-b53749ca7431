"""
Chapter file management and batch processing utilities
"""
import re
import logging
from pathlib import Path
from typing import List, Optional

# 配置日志
logger = logging.getLogger(__name__)


def get_batch_previous_context(output_path: Path, first_chapter_num: int) -> str:
    """获取批次处理的前文上下文"""
    pre_text = '无前文'
    if first_chapter_num > 1:
        # 查找上一个批次的输出文件
        previous_batch_files = sorted(
            [f for f in output_path.glob('*_rewrite.txt')],
            key=lambda x: int(re.search(r'chapter_(\d+)_\d+_rewrite\.txt', x.name).group(1)) if re.search(r'chapter_(\d+)_\d+_rewrite\.txt', x.name) else 0
        )

        if previous_batch_files:
            # 取最后一个处理过的批次文件的后500字符作为pre_text
            last_batch_file = previous_batch_files[-1]
            try:
                pre_text = last_batch_file.read_text(encoding='utf-8')[-500:]
                logger.info(f"Retrieved pre_text from batch: {last_batch_file.name}")
            except Exception as e:
                logger.error(f"Error reading batch file {last_batch_file.name}: {str(e)}")
                pre_text = '无前文'

    return pre_text


def combine_batch_chapters(batch_files: List[Path]) -> tuple[str, List[str]]:
    """合并批次中的所有章节内容"""
    combined_text = ""
    chapter_info = []

    for file_path in batch_files:
        try:
            chapter_text = file_path.read_text(encoding='utf-8')
            chapter_num = int(re.search(r'\d+', file_path.stem).group())
            combined_text += f"\n\n=== 第{chapter_num}章 ===\n\n{chapter_text}"
            chapter_info.append(f"第{chapter_num}章")
            logger.info(f"Added chapter {chapter_num} to batch (length: {len(chapter_text)} characters)")
        except Exception as e:
            logger.error(f"Error reading file {file_path.name}: {str(e)}")
            continue

    return combined_text, chapter_info


def filter_chapter_files(input_files: List[Path], start_chapter: Optional[int] = None, end_chapter: Optional[int] = None) -> List[Path]:
    """根据章节范围过滤文件"""
    if start_chapter is None and end_chapter is None:
        return input_files

    filtered_files = []
    for f in input_files:
        chapter_num = int(re.search(r'\d+', f.stem).group())
        if start_chapter is not None and chapter_num < start_chapter:
            continue
        if end_chapter is not None and chapter_num > end_chapter:
            continue
        filtered_files.append(f)

    return filtered_files


def get_chapter_files_sorted(input_path: Path, exclude_rewrite: bool = True) -> List[Path]:
    """获取排序后的章节文件列表"""
    if exclude_rewrite:
        files = [f for f in input_path.glob('*.txt') if 'rewrite' not in f.stem]
    else:
        files = list(input_path.glob('*.txt'))

    return sorted(files, key=lambda x: int(re.search(r'\d+', x.stem).group()))


def extract_chapter_number(file_path: Path) -> int:
    """从文件路径中提取章节号"""
    match = re.search(r'\d+', file_path.stem)
    if match:
        return int(match.group())
    raise ValueError(f"Cannot extract chapter number from {file_path.name}")


def check_processed_chapters(input_files: List[Path], output_path: Path) -> tuple[int, List[Path]]:
    """检查已处理的章节并返回待处理的文件列表"""
    already_processed = 0
    pending_files = []

    for file_path in input_files:
        chapter_num = extract_chapter_number(file_path)
        output_name = f"chapter_{chapter_num:03d}_rewrite.txt"
        output_file_path = output_path / output_name

        if output_file_path.exists():
            logger.info(f"Skipping already processed chapter: {chapter_num}")
            already_processed += 1
        else:
            pending_files.append(file_path)

    return already_processed, pending_files
